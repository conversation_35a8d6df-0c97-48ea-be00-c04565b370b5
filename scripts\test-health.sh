#!/bin/bash

# Health Check Test Script
# This script tests all health check endpoints to verify they're working correctly

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARN: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Test function
test_endpoint() {
    local url="$1"
    local description="$2"
    local expected_status="${3:-200}"
    
    info "Testing $description: $url"
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_test_response.txt "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log "✓ $description - HTTP $response"
        if [ -f /tmp/health_test_response.txt ]; then
            local content=$(cat /tmp/health_test_response.txt)
            if [ ${#content} -lt 200 ]; then
                info "  Response: $content"
            else
                info "  Response: ${content:0:100}..."
            fi
        fi
        return 0
    else
        error "✗ $description - HTTP $response (expected $expected_status)"
        if [ -f /tmp/health_test_response.txt ]; then
            error "  Response: $(cat /tmp/health_test_response.txt)"
        fi
        return 1
    fi
}

# Test Docker container health
test_docker_health() {
    local container="$1"
    info "Testing Docker health status for $container"
    
    if ! docker ps --format "table {{.Names}}" | grep -q "$container"; then
        error "✗ Container $container is not running"
        return 1
    fi
    
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "none")
    
    case "$health_status" in
        "healthy")
            log "✓ Container $container is healthy"
            return 0
            ;;
        "unhealthy")
            error "✗ Container $container is unhealthy"
            return 1
            ;;
        "starting")
            warn "⚠ Container $container is still starting"
            return 1
            ;;
        "none")
            warn "⚠ Container $container has no health check configured"
            return 0
            ;;
        *)
            error "✗ Container $container has unknown health status: $health_status"
            return 1
            ;;
    esac
}

# Main test function
main() {
    log "Starting health check tests..."
    
    local failed_tests=0
    local total_tests=0
    
    # Test 1: Docker container health
    total_tests=$((total_tests + 1))
    if ! test_docker_health "heibooky-web"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if ! test_docker_health "heibooky-nginx"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 2: Direct Django health checks
    total_tests=$((total_tests + 1))
    if docker exec heibooky-web curl -f -s "http://localhost:8000/health/" > /dev/null 2>&1; then
        log "✓ Direct Django health check (/health/)"
    else
        error "✗ Direct Django health check (/health/)"
        failed_tests=$((failed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if docker exec heibooky-web curl -f -s "http://localhost:8000/monitoring/health/" > /dev/null 2>&1; then
        log "✓ Direct Django health check (/monitoring/health/)"
    else
        error "✗ Direct Django health check (/monitoring/health/)"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 3: HTTP health checks (through nginx)
    total_tests=$((total_tests + 1))
    if ! test_endpoint "http://localhost/health/" "HTTP health check"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if ! test_endpoint "http://localhost/monitoring/health/" "HTTP monitoring health check"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 4: HTTPS health checks (if SSL is configured)
    if curl -k -s "https://localhost/health/" > /dev/null 2>&1; then
        total_tests=$((total_tests + 1))
        if ! test_endpoint "https://localhost/health/" "HTTPS health check"; then
            failed_tests=$((failed_tests + 1))
        fi
    else
        info "Skipping HTTPS tests (SSL not configured or not accessible)"
    fi
    
    # Test 5: Additional monitoring endpoints
    total_tests=$((total_tests + 1))
    if ! test_endpoint "http://localhost/monitoring/ready/" "Readiness check"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    total_tests=$((total_tests + 1))
    if ! test_endpoint "http://localhost/monitoring/alive/" "Liveness check"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Summary
    echo ""
    log "Health check test summary:"
    log "Total tests: $total_tests"
    log "Passed: $((total_tests - failed_tests))"
    
    if [ $failed_tests -eq 0 ]; then
        log "All health checks passed! ✓"
        return 0
    else
        error "Failed tests: $failed_tests"
        error "Some health checks failed! ✗"
        return 1
    fi
}

# Cleanup function
cleanup() {
    rm -f /tmp/health_test_response.txt
}

# Set up cleanup trap
trap cleanup EXIT

# Run main function
main "$@"
